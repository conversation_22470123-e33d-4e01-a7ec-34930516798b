<!DOCTYPE html>
<html>
<head>
    <title>Test Merge Sort Recursive</title>
</head>
<body>
    <h1>Test Merge Sort with Inversion Count</h1>
    <pre id="output"></pre>
    
    <script type="module">
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const mergeSortCode = `#include <iostream>
using namespace std;

int mergeAndCount(int arr[], int left, int mid, int right) {
    int i = left;
    int j = mid + 1;
    int k = left;
    int inv_count = 0;

    int temp[right + 1];
    
    while (i <= mid && j <= right) {
        if (arr[i] <= arr[j]) {
            temp[k++] = arr[i++];
        } else {
            temp[k++] = arr[j++];
            inv_count += (mid - i + 1);
        }
    }

    while (i <= mid) {
        temp[k++] = arr[i++];
    }
    
    while (j <= right) {
        temp[k++] = arr[j++];
    }
    
    for (int i = left; i <= right; i++) {
        arr[i] = temp[i];
    }

    return inv_count;
}

int mergeSortAndCount(int arr[], int left, int right) {
    int inv_count = 0;
    if (left < right) {
        int mid = left + (right - left) / 2;

        inv_count += mergeSortAndCount(arr, left, mid);
        inv_count += mergeSortAndCount(arr, mid + 1, right);
        inv_count += mergeAndCount(arr, left, mid, right);
    }
    return inv_count;
}`;

        console.log('=== TESTING MERGE SORT RECURSIVE ===');
        
        try {
            const parser = new CppParser();
            const result = parser.parse(mergeSortCode);
            
            console.log('Time Complexity:', result.complexity);
            console.log('Expected: O(n log n)');
            console.log('Recursive Calls:', result.details.recursiveCalls);
            console.log('Patterns:', result.details.patterns);
            console.log('Max Nesting:', result.details.maxNesting);
            console.log('Loop Count:', result.details.loopCount);
            
            const output = document.getElementById('output');
            output.textContent = `
Merge Sort Recursive Analysis:
=============================

Time Complexity: ${result.complexity}
Expected: O(n log n)
Status: ${result.complexity === 'O(n log n)' ? '✅ CORRECT' : '❌ WRONG - Should be O(n log n)'}

Recursive Calls: ${result.details.recursiveCalls}
Expected: 2 (divide and conquer)

Confidence: ${result.confidence}%
Explanation: ${result.explanation}

Patterns detected:
${result.details.patterns.join('\n')}

ANALYSIS:
This is a classic divide-and-conquer merge sort:
- mergeSortAndCount calls itself twice (divide)
- Each call processes half the array
- mergeAndCount combines results in O(n) time
- Total: T(n) = 2T(n/2) + O(n) = O(n log n)

The parser should detect:
1. Two recursive calls in mergeSortAndCount
2. Divide-and-conquer pattern (mid calculation)
3. Binary search-like recursion but with linear work per level
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
