import { CppParser } from './src/utils/parsers/cppParser.js';

const mergeSortCode = `#include <iostream>
using namespace std;

int mergeAndCount(int arr[], int left, int mid, int right) {
    int i = left;
    int j = mid + 1;
    int k = left;
    int inv_count = 0;

    int temp[right + 1];
    
    while (i <= mid && j <= right) {
        if (arr[i] <= arr[j]) {
            temp[k++] = arr[i++];
        } else {
            temp[k++] = arr[j++];
            inv_count += (mid - i + 1);
        }
    }

    while (i <= mid) {
        temp[k++] = arr[i++];
    }
    
    while (j <= right) {
        temp[k++] = arr[j++];
    }
    
    for (int i = left; i <= right; i++) {
        arr[i] = temp[i];
    }

    return inv_count;
}

int mergeSortAndCount(int arr[], int left, int right) {
    int inv_count = 0;
    if (left < right) {
        int mid = left + (right - left) / 2;

        inv_count += mergeSortAndCount(arr, left, mid);
        inv_count += mergeSortAndCount(arr, mid + 1, right);
        inv_count += mergeAndCount(arr, left, mid, right);
    }
    return inv_count;
}`;

console.log('=== TESTING MERGE SORT SPACE COMPLEXITY FIX ===');

try {
    const parser = new CppParser();
    const result = parser.parse(mergeSortCode);
    
    console.log('Time Complexity:', result.complexity);
    console.log('Space Complexity:', result.space?.spaceComplexity);
    console.log('Space Explanation:', result.space?.explanation);
    console.log('Space Confidence:', result.space?.confidence + '%');
    console.log('Recursive Calls:', result.details.recursiveCalls);
    console.log('Space Patterns:', result.space?.details?.patterns);
    console.log('Expected Space: O(n) (due to auxiliary arrays + O(log n) recursion)');
    console.log('Status:', result.space?.spaceComplexity === 'O(n)' ? '✅ CORRECT' : '❌ WRONG - Still showing ' + result.space?.spaceComplexity);
    
    console.log('\n=== DETAILED ANALYSIS ===');
    console.log('Space Details:', JSON.stringify(result.space?.details, null, 2));
    
} catch (error) {
    console.error('Error:', error.message);
}
