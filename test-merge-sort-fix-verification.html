<!DOCTYPE html>
<html>
<head>
    <title>Verify Merge Sort Fix</title>
</head>
<body>
    <h1>Verify Merge Sort Time Complexity Fix</h1>
    <pre id="output"></pre>
    
    <script type="module">
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const yourMergeSortCode = `#include <iostream>
using namespace std;

int mergeAndCount(int arr[], int left, int mid, int right) {
    int i = left; // Starting index for left subarray
    int j = mid + 1; // Starting index for right subarray
    int k = left; // Starting index to be sorted
    int inv_count = 0;

    // Create a temporary array
    int temp[right + 1];
    
    while (i <= mid && j <= right) {
        if (arr[i] <= arr[j]) {
            temp[k++] = arr[i++];
        } else {
            temp[k++] = arr[j++];
            inv_count += (mid - i + 1); // Count inversions
        }
    }

    // Copy remaining elements of left subarray
    while (i <= mid) {
        temp[k++] = arr[i++];
    }
    
    // Copy remaining elements of right subarray
    while (j <= right) {
        temp[k++] = arr[j++];
    }
    
    // Copy sorted subarray into Original array
    for (int i = left; i <= right; i++) {
        arr[i] = temp[i];
    }

    return inv_count;
}

int mergeSortAndCount(int arr[], int left, int right) {
    int inv_count = 0;
    if (left < right) {
        int mid = left + (right - left) / 2;

        inv_count += mergeSortAndCount(arr, left, mid);
        inv_count += mergeSortAndCount(arr, mid + 1, right);
        inv_count += mergeAndCount(arr, left, mid, right);
    }
    return inv_count;
}

int main() {
    int n;
    
    cout << "Enter the number of elements in the array: ";
    cin >> n;

    int* arr = new int[n]; // Dynamically allocate array

    cout << "Enter the elements of the array:\\n";
    for (int i = 0; i < n; i++) {
        cin >> arr[i];
    }

    cout << "Number of inversions: " << mergeSortAndCount(arr, 0, n - 1) << endl;

    delete[] arr; // Free dynamically allocated memory
    return 0;
}`;

        console.log('=== TESTING MERGE SORT FIX ===');
        
        try {
            const parser = new CppParser();
            const result = parser.parse(yourMergeSortCode);
            
            console.log('Analysis Results:');
            console.log('Time Complexity:', result.complexity);
            console.log('Expected: O(n log n)');
            console.log('Recursive Calls:', result.details.recursiveCalls);
            console.log('Patterns:', result.details.patterns);
            console.log('Confidence:', result.confidence + '%');
            console.log('Explanation:', result.explanation);
            
            const isCorrect = result.complexity === 'O(n log n)';
            
            const output = document.getElementById('output');
            output.textContent = `
MERGE SORT TIME COMPLEXITY ANALYSIS
===================================

Your Code Analysis:
Time Complexity: ${result.complexity}
Expected: O(n log n)
Status: ${isCorrect ? '✅ CORRECT!' : '❌ INCORRECT - Still showing ' + result.complexity}

Details:
- Recursive Calls Detected: ${result.details.recursiveCalls}
- Function Analyzed: mergeSortAndCount (should be the main recursive function)
- Confidence: ${result.confidence}%

Explanation: ${result.explanation}

Patterns Detected:
${result.details.patterns.join('\n')}

${isCorrect ? 
  'SUCCESS: The parser now correctly identifies your merge sort code as O(n log n)!' :
  'ISSUE: The parser is still not correctly identifying the merge sort pattern.'}

ALGORITHM ANALYSIS:
Your code implements merge sort with inversion counting:
1. mergeSortAndCount() divides the array recursively (2 calls per level)
2. mergeAndCount() merges subarrays in linear time O(n)
3. Total: T(n) = 2T(n/2) + O(n) = O(n log n)
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
