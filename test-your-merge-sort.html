<!DOCTYPE html>
<html>
<head>
    <title>Test Your Merge Sort Code</title>
</head>
<body>
    <h1>Test Your Exact Merge Sort Code</h1>
    <pre id="output"></pre>
    
    <script type="module">
        import { CppParser } from './src/utils/parsers/cppParser.js';
        
        const yourCode = `#include <iostream>
using namespace std;

int mergeAndCount(int arr[], int left, int mid, int right) {
    int i = left; // Starting index for left subarray
    int j = mid + 1; // Starting index for right subarray
    int k = left; // Starting index to be sorted
    int inv_count = 0;

    // Create a temporary array
    int temp[right + 1];
    
    while (i <= mid && j <= right) {
        if (arr[i] <= arr[j]) {
            temp[k++] = arr[i++];
        } else {
            temp[k++] = arr[j++];
            inv_count += (mid - i + 1); // Count inversions
        }
    }

    // Copy remaining elements of left subarray
    while (i <= mid) {
        temp[k++] = arr[i++];
    }
    
    // Copy remaining elements of right subarray
    while (j <= right) {
        temp[k++] = arr[j++];
    }
    
    // Copy sorted subarray into Original array
    for (int i = left; i <= right; i++) {
        arr[i] = temp[i];
    }

    return inv_count;
}

int mergeSortAndCount(int arr[], int left, int right) {
    int inv_count = 0;
    if (left < right) {
        int mid = left + (right - left) / 2;

        inv_count += mergeSortAndCount(arr, left, mid);
        inv_count += mergeSortAndCount(arr, mid + 1, right);
        inv_count += mergeAndCount(arr, left, mid, right);
    }
    return inv_count;
}

int main() {
    int n;
    
    cout << "Enter the number of elements in the array: ";
    cin >> n;

    int* arr = new int[n]; // Dynamically allocate array

    cout << "Enter the elements of the array:\\n";
    for (int i = 0; i < n; i++) {
        cin >> arr[i];
    }

    cout << "Number of inversions: " << mergeSortAndCount(arr, 0, n - 1) << endl;

    delete[] arr; // Free dynamically allocated memory
    return 0;
}`;

        console.log('=== TESTING YOUR EXACT MERGE SORT CODE ===');
        
        try {
            const parser = new CppParser();
            const result = parser.parse(yourCode);
            
            console.log('Time Complexity:', result.complexity);
            console.log('Expected: O(n log n)');
            console.log('Space Complexity:', result.space?.spaceComplexity);
            console.log('Recursive Calls:', result.details.recursiveCalls);
            console.log('Patterns:', result.details.patterns);
            
            const output = document.getElementById('output');
            output.textContent = `
Your Merge Sort Analysis Results:
================================

Time Complexity: ${result.complexity}
Expected: O(n log n)
Status: ${result.complexity === 'O(n log n)' ? '✅ FIXED!' : '❌ Still showing ' + result.complexity}

Space Complexity: ${result.space?.spaceComplexity || 'N/A'}
Expected: O(n)

Recursive Calls: ${result.details.recursiveCalls}
Expected: 2 (in mergeSortAndCount function)

Confidence: ${result.confidence}%
Explanation: ${result.explanation}

Patterns detected:
${result.details.patterns.join('\n')}

ANALYSIS:
Your code implements merge sort with inversion counting:
- mergeSortAndCount: Divides array recursively (2 calls)
- mergeAndCount: Merges subarrays in O(n) time
- Total complexity: T(n) = 2T(n/2) + O(n) = O(n log n)

The parser should now correctly detect this as O(n log n)!
            `;
            
        } catch (error) {
            console.error('Error:', error);
            document.getElementById('output').textContent = 'Error: ' + error.message;
        }
    </script>
</body>
</html>
