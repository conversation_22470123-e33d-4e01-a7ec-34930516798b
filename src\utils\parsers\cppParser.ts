import { ComplexityResult } from '../../types/analysis';

interface CppNode {
  type: string;
  children?: CppNode[];
  value?: string;
  line?: number;
  depth?: number;
}

export class CppParser {
  private patterns: string[] = [];
  private loopCount = 0;
  private maxNesting = 0;
  private recursiveCount = 0;
  private functionName: string | null = null;
  private loopStack: number[] = []; // Track actual nesting depth

  parse(code: string): ComplexityResult {
    this.reset();
    
    try {
      // Use a more robust approach
      return this.analyzeWithBraceTracking(code);
    } catch (error) {
      console.warn('C++ analysis failed:', error);
      return this.getErrorResult();
    }
  }

  private reset() {
    this.patterns = [];
    this.loopCount = 0;
    this.maxNesting = 0;
    this.recursiveCount = 0;
    this.functionName = null;
    this.loopStack = [];
  }

  private analyzeWithBraceTracking(code: string): ComplexityResult {
    // Remove comments first
    const cleanCode = code
      .replace(/\/\/.*$/gm, '')
      .replace(/\/\*[\s\S]*?\*\//g, '');

    const lines = cleanCode.split('\n');

    // Extract function name
    const funcMatch = cleanCode.match(/(?:int|void|double|float|char|string|bool)\s+(\w+)\s*\(/);
    this.functionName = funcMatch ? funcMatch[1] : null;

    // Use a simpler approach: track loop nesting by counting active loops
    const activeLoops: number[] = []; // Stack of line numbers where loops start
    let braceDepth = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Count braces to track scope depth
      const openBraces = (line.match(/{/g) || []).length;
      const closeBraces = (line.match(/}/g) || []).length;

      // Update brace depth FIRST
      braceDepth += openBraces - closeBraces;

      // Remove loops that have ended (when we exit their scope)
      while (activeLoops.length > 0 && braceDepth < activeLoops[activeLoops.length - 1]) {
        activeLoops.pop();
      }

      // Check for loops AFTER updating scope
      if (this.isLoopLine(line)) {
        this.loopCount++;
        activeLoops.push(braceDepth); // Current depth where this loop starts
        const currentNesting = activeLoops.length;
        this.maxNesting = Math.max(this.maxNesting, currentNesting);
        this.patterns.push(`Loop at nesting level ${currentNesting}: ${line.trim()}`);
      }

      // Check for recursion (but not the function declaration itself)
      if (this.functionName && line.includes(this.functionName + '(') &&
          !this.isFunctionDeclaration(line) && !line.includes('//')) {
        // Count actual number of recursive calls in this line
        const callCount = this.countRecursiveCalls(line, this.functionName);
        if (callCount > 0) {
          this.recursiveCount += callCount;
          this.patterns.push(`Recursive call(s): ${line.trim()} (${callCount} calls)`);
        }
      }
    }

    console.log('C++ Parser Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      patterns: this.patterns
    });

    return this.calculateComplexity();
  }

  private isLoopLine(line: string): boolean {
    // More comprehensive loop detection
    const trimmed = line.trim();

    // Standard for loops
    if (/^for\s*\(/.test(trimmed)) return true;

    // While loops
    if (/^while\s*\(/.test(trimmed)) return true;

    // Do-while loops
    if (/^do\s*{/.test(trimmed)) return true;

    // Range-based for loops (C++11)
    if (/^for\s*\(\s*\w+.*:\s*\w+/.test(trimmed)) return true;

    return false;
  }

  private isFunctionDeclaration(line: string): boolean {
    // Check if this line is a function declaration/definition
    const trimmed = line.trim();
    return /^(int|void|double|float|char|string|bool)\s+\w+\s*\(/.test(trimmed);
  }

  private countRecursiveCalls(line: string, functionName: string): number {
    // Count how many times the function is called in this line
    const regex = new RegExp(`\\b${functionName}\\s*\\(`, 'g');
    const matches = line.match(regex);
    return matches ? matches.length : 0;
  }

  private hasActualNestedLoops(): boolean {
    // Check if we actually have nested loops by analyzing the patterns more carefully
    const patterns = this.patterns.join('\n');
    const codeText = patterns.toLowerCase();

    // Count how many times we see each nesting level
    const level1Count = (patterns.match(/nesting level 1/g) || []).length;
    const level2Count = (patterns.match(/nesting level 2/g) || []).length;
    const level3Count = (patterns.match(/nesting level 3/g) || []).length;

    // Check for common nested loop keywords first (high confidence)
    const hasNestedKeywords = codeText.includes('bubble') ||
                             codeText.includes('selection') ||
                             codeText.includes('matrix') ||
                             codeText.includes('2d') ||
                             codeText.includes('nested');

    // If we have clear nested keywords, it's definitely nested
    if (hasNestedKeywords) {
      return true;
    }

    // True nesting means we have loops at level 2+
    const hasActualNesting = level2Count > 0 || level3Count > 0;

    // For merge sort case: if we have exactly 2 loops both at level 1, it's sequential
    // For bubble/selection sort: we should have 1 loop at level 1 and 1 at level 2
    const isSequentialPattern = (level1Count === 2 && level2Count === 0 && level3Count === 0);

    return hasActualNesting && !isSequentialPattern;
  }

  private calculateComplexity(): ComplexityResult {
    let complexity = 'O(1)';
    let explanation = 'Constant time - no loops or recursive calls detected.';
    let confidence = 95;

    // Debug info
    console.log('C++ Analysis Debug:', {
      loopCount: this.loopCount,
      maxNesting: this.maxNesting,
      recursiveCount: this.recursiveCount,
      patterns: this.patterns,
      functionName: this.functionName
    });

    if (this.recursiveCount > 0) {
      // Improved recursion analysis
      complexity = this.analyzeRecursiveComplexity();
      explanation = this.getRecursiveExplanation(complexity);
      confidence = this.getRecursiveConfidence(complexity);
    } else if (this.maxNesting >= 3) {
      complexity = 'O(n^3)';
      explanation = `Cubic time complexity due to ${this.maxNesting} levels of nested loops.`;
      confidence = 95;
    } else if (this.maxNesting >= 2 && this.hasActualNestedLoops()) {
      complexity = 'O(n^2)';
      explanation = 'Quadratic time complexity due to nested loops (like bubble sort, selection sort).';
      confidence = 95;
    } else if (this.loopCount > 0) {
      complexity = 'O(n)';
      explanation = this.loopCount > 1 ?
        'Linear time due to sequential loops (not nested).' :
        'Linear time due to a single loop.';
      confidence = 95;
    }

    return {
      complexity,
      explanation,
      confidence: Math.max(confidence, 70),
      details: {
        loops: this.loopCount,
        nestingLevel: this.maxNesting,
        recursiveCalls: this.recursiveCount,
        patterns: this.patterns
      }
    };
  }

  private analyzeRecursiveComplexity(): string {
    // Check for merge sort pattern (divide-and-conquer with linear work)
    if (this.isMergeSortPattern()) {
      return 'O(n log n)';
    }

    // Check for binary search pattern (divide-and-conquer with constant work)
    if (this.isBinarySearchPattern()) {
      return 'O(log n)';
    }

    // Check for fibonacci-like pattern (multiple recursive calls)
    if (this.recursiveCount >= 2 && this.isFibonacciPattern()) {
      return 'O(2^n)';
    }

    // Check for tree traversal or single recursive call
    if (this.recursiveCount === 1 || this.isTreeTraversalPattern()) {
      return 'O(n)';
    }

    // Multiple recursive calls but not fibonacci-like
    if (this.recursiveCount > 1) {
      return 'O(2^n)';
    }

    return 'O(n)';
  }

  private isMergeSortPattern(): boolean {
    // Look for patterns indicating merge sort (divide-and-conquer with linear work)
    const patterns = this.patterns.join(' ').toLowerCase();
    const codeText = patterns;

    // Key indicators of merge sort:
    // 1. Two recursive calls (divide)
    // 2. Mid calculation for splitting
    // 3. Merge operation or linear work per level
    // 4. Function names containing "merge" or "sort"

    const hasTwoRecursiveCalls = this.recursiveCount === 2;
    const hasMidCalculation = codeText.includes('mid') &&
                             (codeText.includes('left') || codeText.includes('right'));
    const hasMergeKeywords = codeText.includes('merge') ||
                            codeText.includes('sort') ||
                            codeText.includes('temp') ||
                            codeText.includes('combine');
    const hasLinearWork = codeText.includes('while') ||
                         codeText.includes('for') ||
                         codeText.includes('copy');

    // Strong indicators: 2 recursive calls + mid calculation + merge work
    return hasTwoRecursiveCalls && hasMidCalculation && (hasMergeKeywords || hasLinearWork);
  }

  private isBinarySearchPattern(): boolean {
    // Look for patterns indicating binary search (divide-and-conquer with constant work)
    const patterns = this.patterns.join(' ').toLowerCase();

    // Binary search has mid calculation but NO merge work and typically 1 recursive call
    const hasMidCalculation = patterns.includes('mid') ||
                             patterns.includes('l + (r - l)');
    const hasSearchPattern = patterns.includes('arr[mid]') ||
                            patterns.includes('target') ||
                            patterns.includes('search') ||
                            patterns.includes('find');
    const hasConstantWork = this.recursiveCount <= 1 ||
                           (!patterns.includes('merge') && !patterns.includes('temp'));

    return hasMidCalculation && hasConstantWork && (hasSearchPattern || this.recursiveCount <= 1);
  }

  private isFibonacciPattern(): boolean {
    // Look for fibonacci-like patterns
    const patterns = this.patterns.join(' ').toLowerCase();
    const codeText = patterns;

    // Check for classic fibonacci patterns
    const hasFibonacciName = codeText.includes('fibonacci');
    const hasNMinus1And2 = codeText.includes('n - 1') && codeText.includes('n - 2');
    const hasTwoRecursiveCalls = this.recursiveCount >= 2;
    const hasReturnStatement = codeText.includes('return');

    // Strong indicators of fibonacci-like exponential recursion
    return hasFibonacciName ||
           (hasNMinus1And2 && hasTwoRecursiveCalls) ||
           (hasTwoRecursiveCalls && hasReturnStatement && codeText.includes('(2 calls)'));
  }

  private isTreeTraversalPattern(): boolean {
    // Look for tree traversal patterns
    const patterns = this.patterns.join(' ').toLowerCase();
    return patterns.includes('left') && patterns.includes('right') ||
           patterns.includes('node') ||
           patterns.includes('tree');
  }

  private getRecursiveExplanation(complexity: string): string {
    switch (complexity) {
      case 'O(n log n)':
        return 'Linearithmic time due to divide-and-conquer with linear work per level (like merge sort).';
      case 'O(log n)':
        return 'Logarithmic time due to divide-and-conquer with constant work per level (like binary search).';
      case 'O(2^n)':
        return 'Exponential time due to multiple recursive calls without memoization (like naive Fibonacci).';
      case 'O(n)':
        return 'Linear time due to recursive processing of each element (like tree traversal).';
      default:
        return 'Recursive algorithm detected.';
    }
  }

  private getRecursiveConfidence(complexity: string): number {
    switch (complexity) {
      case 'O(n log n)':
        return this.isMergeSortPattern() ? 95 : 80;
      case 'O(log n)':
        return this.isBinarySearchPattern() ? 90 : 75;
      case 'O(2^n)':
        return this.isFibonacciPattern() ? 90 : 80;
      case 'O(n)':
        return 85;
      default:
        return 70;
    }
  }

  private getErrorResult(): ComplexityResult {
    return {
      complexity: 'O(?)',
      explanation: 'Analysis failed. Please check your code syntax.',
      confidence: 0,
      details: {
        loops: 0,
        nestingLevel: 0,
        recursiveCalls: 0,
        patterns: ['Analysis error']
      }
    };
  }
}