import { SpaceComplexityResult, SupportedLanguage } from '../types/analysis';

export class SpaceComplexityAnalyzer {
  analyzeSpace(code: string, language: SupportedLanguage, recursiveCalls: number): SpaceComplexityResult {
    const cleanCode = this.cleanCode(code);

    switch (language) {
      case 'cpp':
        return this.analyzeCppSpace(cleanCode, recursiveCalls);
      case 'python':
        return this.analyzePythonSpace(cleanCode, recursiveCalls);
      case 'javascript':
        return this.analyzeJavaScriptSpace(cleanCode, recursiveCalls);
      default:
        return this.getDefaultSpaceResult();
    }
  }

  private cleanCode(code: string): string {
    return code
      .replace(/\/\/.*$/gm, '') // Remove C++ style comments
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove C++ block comments
      .replace(/#.*$/gm, '') // Remove Python comments
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .join('\n');
  }

  private analyzeCppSpace(code: string, recursiveCalls: number): SpaceComplexityResult {
    const lines = code.split('\n');
    let variables = 0;
    let dataStructures: string[] = [];
    let patterns: string[] = [];
    let auxiliarySpace = 'O(1)';

    for (const line of lines) {
      // Count variable declarations
      if (this.isVariableDeclaration(line, 'cpp')) {
        variables++;
        patterns.push(`Variable declaration: ${line}`);
      }

      // Detect data structures
      const detectedDS = this.detectDataStructures(line, 'cpp');
      if (detectedDS.length > 0) {
        dataStructures.push(...detectedDS);
        patterns.push(`Data structure: ${line}`);
      }

      // Detect dynamic memory allocation
      if (this.isDynamicAllocation(line, 'cpp')) {
        auxiliarySpace = 'O(n)';
        patterns.push(`Dynamic allocation: ${line}`);
      }
    }

    return this.calculateSpaceComplexity(variables, dataStructures, recursiveCalls, auxiliarySpace, patterns);
  }

  private analyzePythonSpace(code: string, recursiveCalls: number): SpaceComplexityResult {
    const lines = code.split('\n');
    let variables = 0;
    let dataStructures: string[] = [];
    let patterns: string[] = [];
    let auxiliarySpace = 'O(1)';

    for (const line of lines) {
      // Count variable assignments
      if (this.isVariableDeclaration(line, 'python')) {
        variables++;
        patterns.push(`Variable assignment: ${line}`);
      }

      // Detect data structures
      const detectedDS = this.detectDataStructures(line, 'python');
      if (detectedDS.length > 0) {
        dataStructures.push(...detectedDS);
        patterns.push(`Data structure: ${line}`);
        
        // Python lists/dicts typically use O(n) space
        if (detectedDS.some(ds => ['list', 'dict', 'set'].includes(ds))) {
          auxiliarySpace = 'O(n)';
        }
      }

      // Detect list comprehensions
      if (this.isListComprehension(line)) {
        auxiliarySpace = 'O(n)';
        patterns.push(`List comprehension: ${line}`);
      }
    }

    return this.calculateSpaceComplexity(variables, dataStructures, recursiveCalls, auxiliarySpace, patterns);
  }

  private analyzeJavaScriptSpace(code: string, recursiveCalls: number): SpaceComplexityResult {
    const lines = code.split('\n');
    let variables = 0;
    let dataStructures: string[] = [];
    let patterns: string[] = [];
    let auxiliarySpace = 'O(1)';

    for (const line of lines) {
      // Count variable declarations
      if (this.isVariableDeclaration(line, 'javascript')) {
        variables++;
        patterns.push(`Variable declaration: ${line}`);
      }

      // Detect data structures
      const detectedDS = this.detectDataStructures(line, 'javascript');
      if (detectedDS.length > 0) {
        dataStructures.push(...detectedDS);
        patterns.push(`Data structure: ${line}`);
      }

      // Detect array methods that create new arrays
      if (this.isArrayMethod(line)) {
        auxiliarySpace = 'O(n)';
        patterns.push(`Array method creating new array: ${line}`);
      }
    }

    return this.calculateSpaceComplexity(variables, dataStructures, recursiveCalls, auxiliarySpace, patterns);
  }

  private isVariableDeclaration(line: string, language: SupportedLanguage): boolean {
    switch (language) {
      case 'cpp':
        return /^\s*(int|double|float|char|string|bool|auto)\s+\w+/.test(line) ||
               /^\s*\w+\s+\w+\s*=/.test(line);
      case 'python':
        return /^\s*\w+\s*=/.test(line) && !line.includes('==');
      case 'javascript':
        return /^\s*(let|const|var)\s+\w+/.test(line);
      default:
        return false;
    }
  }

  private detectDataStructures(line: string, language: SupportedLanguage): string[] {
    const structures: string[] = [];

    switch (language) {
      case 'cpp':
        if (/vector|array|list|deque|stack|queue|set|map|unordered_/.test(line)) {
          if (line.includes('vector')) structures.push('vector');
          if (line.includes('array')) structures.push('array');
          if (line.includes('list')) structures.push('list');
          if (line.includes('stack')) structures.push('stack');
          if (line.includes('queue')) structures.push('queue');
          if (line.includes('set')) structures.push('set');
          if (line.includes('map')) structures.push('map');
        }
        // Detect auxiliary arrays like int L[n1], R[n2]
        if (/\w+\s+\w+\[\w+\]/.test(line) && !line.includes('//')) {
          structures.push('auxiliary_array');
        }
        break;
      case 'python':
        if (/\[\]|\{\}|list\(|dict\(|set\(/.test(line)) {
          if (line.includes('[]') || line.includes('list(')) structures.push('list');
          if (line.includes('{}') || line.includes('dict(')) structures.push('dict');
          if (line.includes('set(')) structures.push('set');
        }
        break;
      case 'javascript':
        if (/\[\]|\{\}|new Array|new Map|new Set/.test(line)) {
          if (line.includes('[]') || line.includes('Array')) structures.push('array');
          if (line.includes('{}') || line.includes('Object')) structures.push('object');
          if (line.includes('Map')) structures.push('map');
          if (line.includes('Set')) structures.push('set');
        }
        break;
    }
    
    return structures;
  }

  private isDynamicAllocation(line: string, language: SupportedLanguage): boolean {
    switch (language) {
      case 'cpp':
        return /new\s+|malloc|calloc|realloc/.test(line);
      case 'python':
        return /\*\s*\w+/.test(line); // List multiplication
      case 'javascript':
        return /new\s+Array\(/.test(line);
      default:
        return false;
    }
  }

  private isListComprehension(line: string): boolean {
    return /\[.*for\s+\w+\s+in\s+.*\]/.test(line);
  }

  private isArrayMethod(line: string): boolean {
    return /\.(map|filter|reduce|slice|concat)\s*\(/.test(line);
  }

  private calculateSpaceComplexity(
    variables: number,
    dataStructures: string[],
    recursiveCalls: number,
    auxiliarySpace: string,
    patterns: string[]
  ): SpaceComplexityResult {
    let spaceComplexity = 'O(1)';
    let explanation = 'Constant space - only using a fixed amount of extra memory.';
    let confidence = 95;

    // Determine recursion depth impact
    let recursionDepth = 0;
    if (recursiveCalls > 0) {
      // Check for specific algorithm patterns first
      const patternText = patterns.join(' ').toLowerCase();

      if (this.isMergeSortPattern(patternText, recursiveCalls)) {
        // Merge sort: O(log n) recursion depth + O(n) auxiliary space = O(n)
        recursionDepth = Math.ceil(Math.log2(10)); // Approximate log n for display
        spaceComplexity = 'O(n)';
        explanation = 'Linear space due to auxiliary arrays for merging (O(n)) plus logarithmic recursion depth (O(log n)).';
        confidence = 95;
      } else if (this.isQuickSortPattern(patternText, recursiveCalls)) {
        // Quick sort: O(log n) average recursion depth, O(1) auxiliary space
        recursionDepth = Math.ceil(Math.log2(10)); // Approximate log n for display
        spaceComplexity = 'O(log n)';
        explanation = 'Logarithmic space due to recursion stack depth in divide-and-conquer approach.';
        confidence = 90;
      } else if (this.isBinarySearchPattern(patternText, recursiveCalls)) {
        // Binary search: O(log n) recursion depth
        recursionDepth = Math.ceil(Math.log2(10)); // Approximate log n for display
        spaceComplexity = 'O(log n)';
        explanation = 'Logarithmic space due to recursion stack depth in binary search.';
        confidence = 95;
      } else {
        // General recursive algorithms
        recursionDepth = Math.min(recursiveCalls, 10); // Cap for display purposes

        if (recursiveCalls === 1) {
          // Single recursive call (like linear search, factorial)
          spaceComplexity = 'O(n)';
          explanation = 'Linear space due to recursion stack depth.';
          confidence = 90;
        } else if (recursiveCalls === 2) {
          // Binary recursion (like fibonacci, binary tree traversal)
          spaceComplexity = 'O(n)';
          explanation = 'Linear space due to recursion stack depth (not exponential space).';
          confidence = 85;
        } else {
          // Multiple recursive calls
          spaceComplexity = 'O(n)';
          explanation = 'Linear space due to recursion stack depth.';
          confidence = 80;
        }
      }
    }

    // Check for data structures that use additional space
    if (dataStructures.length > 0) {
      const hasLinearDS = dataStructures.some(ds =>
        ['vector', 'array', 'list', 'dict', 'set', 'map', 'auxiliary_array'].includes(ds)
      );

      if (hasLinearDS) {
        if (spaceComplexity === 'O(1)') {
          spaceComplexity = 'O(n)';
          explanation = 'Linear space due to auxiliary data structures.';
          confidence = 90;
        } else if (spaceComplexity === 'O(n)' && dataStructures.includes('auxiliary_array')) {
          explanation = 'Linear space due to auxiliary arrays and recursion stack.';
        }
      }
    }

    // Override with auxiliary space if it's higher
    if (auxiliarySpace === 'O(n)' && spaceComplexity === 'O(1)') {
      spaceComplexity = 'O(n)';
      explanation = 'Linear space due to auxiliary data structures or dynamic allocation.';
      confidence = 85;
    }

    // Adjust confidence based on complexity
    if (patterns.length > 3) {
      confidence -= 10;
    }

    return {
      spaceComplexity,
      explanation,
      confidence: Math.max(confidence, 60),
      details: {
        variables,
        dataStructures: [...new Set(dataStructures)], // Remove duplicates
        recursionDepth,
        auxiliarySpace,
        patterns
      }
    };
  }

  private getDefaultSpaceResult(): SpaceComplexityResult {
    return {
      spaceComplexity: 'O(?)',
      explanation: 'Unable to determine space complexity.',
      confidence: 0,
      details: {
        variables: 0,
        dataStructures: [],
        recursionDepth: 0,
        auxiliarySpace: 'O(?)',
        patterns: ['Analysis failed']
      }
    };
  }

  // Pattern detection methods for algorithm-specific space complexity
  private isMergeSortPattern(patternText: string, recursiveCalls: number): boolean {
    const hasTwoRecursiveCalls = recursiveCalls === 2;
    const hasMidCalculation = patternText.includes('mid') &&
                             (patternText.includes('left') || patternText.includes('right'));
    const hasMergeKeywords = patternText.includes('merge') ||
                            patternText.includes('sort') ||
                            patternText.includes('temp') ||
                            patternText.includes('auxiliary') ||
                            patternText.includes('combine');
    const hasLinearWork = patternText.includes('while') ||
                         patternText.includes('for') ||
                         patternText.includes('copy');

    return hasTwoRecursiveCalls && hasMidCalculation && (hasMergeKeywords || hasLinearWork);
  }

  private isQuickSortPattern(patternText: string, recursiveCalls: number): boolean {
    const hasTwoRecursiveCalls = recursiveCalls === 2;
    const hasPartitionKeywords = patternText.includes('partition') ||
                                patternText.includes('pivot') ||
                                patternText.includes('quicksort') ||
                                patternText.includes('quick_sort');
    const hasSwapOperations = patternText.includes('swap') ||
                             patternText.includes('exchange');

    return hasTwoRecursiveCalls && (hasPartitionKeywords || hasSwapOperations);
  }

  private isBinarySearchPattern(patternText: string, recursiveCalls: number): boolean {
    const hasRecursion = recursiveCalls > 0;
    const hasMidCalculation = patternText.includes('mid');
    const hasBinaryKeywords = patternText.includes('binary') ||
                             patternText.includes('search') ||
                             patternText.includes('find');
    const hasLeftRightComparison = (patternText.includes('left') && patternText.includes('right')) ||
                                  (patternText.includes('low') && patternText.includes('high'));

    return hasRecursion && hasMidCalculation && (hasBinaryKeywords || hasLeftRightComparison);
  }
}
